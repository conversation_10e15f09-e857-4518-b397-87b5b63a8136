// 日次部門別レポート JavaScript

class ReportManager {
    constructor() {
        this.currentData = [];
        this.filterOptions = {};
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.init();
    }

    init() {
        // DOM要素の取得
        this.startDateInput = document.getElementById('startDate');
        this.endDateInput = document.getElementById('endDate');
        this.companySelect = document.getElementById('companySelect');
        this.storeSelect = document.getElementById('storeSelect');
        this.departmentSelect = document.getElementById('departmentSelect');
        this.sectionSelect = document.getElementById('sectionSelect');
        this.searchBtn = document.getElementById('searchBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.loadingDiv = document.getElementById('loading');
        this.errorDiv = document.getElementById('error');
        this.tableContainer = document.getElementById('tableContainer');
        this.tableInfo = document.getElementById('tableInfo');

        // イベントリスナーの設定
        this.setupEventListeners();

        // 初期日付設定
        this.setDefaultDates();

        // フィルター選択肢読み込み
        this.loadFilterOptions();
    }

    setupEventListeners() {
        // 検索ボタン
        this.searchBtn.addEventListener('click', () => {
            if (this.validateFilters()) {
                this.loadData();
            }
        });

        // エクスポートボタン
        this.exportBtn.addEventListener('click', () => {
            this.exportToCSV();
        });

        // 部門選択時の課フィルタ更新
        this.departmentSelect.addEventListener('change', () => {
            this.updateSectionOptions();
        });

        // ソート機能のイベントリスナー
        this.setupSortListeners();

        // Enterキーでの検索
        this.startDateInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && this.validateFilters()) this.loadData();
        });

        this.endDateInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && this.validateFilters()) this.loadData();
        });
    }

    setDefaultDates() {
        // デフォルトで過去30日間を設定
        const today = new Date();
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);

        this.endDateInput.value = this.formatDate(today);
        this.startDateInput.value = this.formatDate(thirtyDaysAgo);
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    async loadFilterOptions() {
        try {
            const response = await fetch('/api/filter-options');
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'フィルター選択肢の取得に失敗しました。');
            }

            this.filterOptions = result.data;
            this.populateFilterOptions();

        } catch (error) {
            console.error('フィルター選択肢読み込みエラー:', error);
            this.showError(error.message);
        }
    }

    populateFilterOptions() {
        // 法人選択肢
        this.companySelect.innerHTML = '<option value="未指定">未指定</option>';
        this.filterOptions.companies.forEach(company => {
            const option = document.createElement('option');
            option.value = company;
            option.textContent = company;
            this.companySelect.appendChild(option);
        });

        // 店舗選択肢
        this.storeSelect.innerHTML = '<option value="未指定">未指定</option>';
        this.filterOptions.stores.forEach(store => {
            const option = document.createElement('option');
            option.value = store;
            option.textContent = store;
            this.storeSelect.appendChild(option);
        });

        // 部門選択肢
        this.departmentSelect.innerHTML = '<option value="未指定">未指定</option>';
        this.filterOptions.departments.forEach(department => {
            const option = document.createElement('option');
            option.value = department;
            option.textContent = department;
            this.departmentSelect.appendChild(option);
        });

        // 初期データ読み込み
        this.loadData();
    }

    updateSectionOptions() {
        const selectedDepartment = this.departmentSelect.value;

        // 課選択肢をクリア
        this.sectionSelect.innerHTML = '<option value="未指定">未指定</option>';

        if (selectedDepartment === '未指定') {
            this.sectionSelect.disabled = true;
            return;
        }

        // 選択された部門の課を取得
        const deptCode = selectedDepartment.split(':')[0];
        const sectionData = this.filterOptions.sections[deptCode];

        if (sectionData && sectionData.sections) {
            this.sectionSelect.disabled = false;
            sectionData.sections.forEach(section => {
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                this.sectionSelect.appendChild(option);
            });
        } else {
            this.sectionSelect.disabled = true;
        }
    }

    validateFilters() {
        // 課が指定されている場合、部門も指定されている必要がある
        const sectionValue = this.sectionSelect.value;
        const departmentValue = this.departmentSelect.value;

        if (sectionValue !== '未指定' && departmentValue === '未指定') {
            this.showError('課を指定する場合は、部門の指定が必須です。');
            return false;
        }

        return true;
    }

    setupSortListeners() {
        // ソート可能なヘッダーにクリックイベントを追加
        document.addEventListener('click', (e) => {
            if (e.target.closest('th.sortable')) {
                const th = e.target.closest('th.sortable');
                const column = th.getAttribute('data-column');
                const dataType = th.getAttribute('data-type');
                this.sortTable(column, dataType, th);
            }
        });
    }

    sortTable(column, dataType, headerElement) {
        // ソート方向の決定
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortDirection = 'asc';
            this.sortColumn = column;
        }

        // ヘッダーのスタイル更新
        this.updateSortHeaders(headerElement);

        // データのソート
        this.currentData.sort((a, b) => {
            let valueA = a[column];
            let valueB = b[column];

            // データタイプに応じた比較
            if (dataType === 'number') {
                valueA = parseFloat(valueA) || 0;
                valueB = parseFloat(valueB) || 0;
            } else {
                valueA = String(valueA || '').toLowerCase();
                valueB = String(valueB || '').toLowerCase();
            }

            let comparison = 0;
            if (valueA > valueB) {
                comparison = 1;
            } else if (valueA < valueB) {
                comparison = -1;
            }

            return this.sortDirection === 'desc' ? comparison * -1 : comparison;
        });

        // テーブルの再描画
        this.renderTable(this.currentData);
    }

    updateSortHeaders(activeHeader) {
        // すべてのソートクラスをクリア
        document.querySelectorAll('th.sortable').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });

        // アクティブなヘッダーにソートクラスを追加
        if (this.sortDirection === 'asc') {
            activeHeader.classList.add('sort-asc');
        } else {
            activeHeader.classList.add('sort-desc');
        }
    }

    resetSort() {
        // ソート状態をリセット
        this.sortColumn = null;
        this.sortDirection = 'asc';

        // すべてのソートクラスをクリア
        document.querySelectorAll('th.sortable').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
    }

    showLoading() {
        this.loadingDiv.style.display = 'block';
        this.errorDiv.style.display = 'none';
        this.tableContainer.style.display = 'none';
    }

    hideLoading() {
        this.loadingDiv.style.display = 'none';
    }

    showError(message) {
        this.errorDiv.innerHTML = `<strong>エラー:</strong> ${message}`;
        this.errorDiv.style.display = 'block';
        this.tableContainer.style.display = 'none';
    }

    showTable() {
        this.errorDiv.style.display = 'none';
        this.tableContainer.style.display = 'block';
    }

    async loadData() {
        try {
            this.showLoading();

            const startDate = this.startDateInput.value;
            const endDate = this.endDateInput.value;

            // 日付バリデーション
            if (!startDate || !endDate) {
                throw new Error('開始日と終了日を入力してください。');
            }

            if (new Date(startDate) > new Date(endDate)) {
                throw new Error('開始日は終了日より前の日付を入力してください。');
            }

            // 絞り込みパラメータの構築
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });

            // 絞り込み条件を追加
            const company = this.companySelect.value;
            const store = this.storeSelect.value;
            const department = this.departmentSelect.value;
            const section = this.sectionSelect.value;

            if (company && company !== '未指定') {
                params.append('company', company);
            }
            if (store && store !== '未指定') {
                params.append('store', store);
            }
            if (department && department !== '未指定') {
                params.append('department', department);
            }
            if (section && section !== '未指定') {
                params.append('section', section);
            }

            // APIリクエスト
            const response = await fetch(`/api/report?${params.toString()}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'データの取得に失敗しました。');
            }

            this.currentData = result.data;
            this.resetSort();
            this.renderTable(result.data);
            this.updateTableInfo(result);
            this.showTable();

        } catch (error) {
            console.error('データ読み込みエラー:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderTable(data) {
        const tableBody = document.getElementById('reportTableBody');

        if (data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="18" style="text-align: center; padding: 40px;">データがありません</td></tr>';
            return;
        }

        const rows = data.map(row => `
            <tr>
                <td>${row.店舗コード}</td>
                <td style="text-align: left;">${row.店舗名}</td>
                <td>${row.部門コード}</td>
                <td style="text-align: left;">${row.部門名称}</td>
                <td>${row.課コード}</td>
                <td style="text-align: left;">${row.課名称}</td>
                <td class="number">${this.formatNumber(row.売上金額)}</td>
                <td class="number">${this.formatNumber(row.仕入原価)}</td>
                <td class="number">${this.formatNumber(row.仕入売価)}</td>
                <td class="number">${this.formatNumber(row.返品原価)}</td>
                <td class="number">${this.formatNumber(row.返品売価)}</td>
                <td class="number">${this.formatNumber(row.出庫原価)}</td>
                <td class="number">${this.formatNumber(row.出庫売価)}</td>
                <td class="number">${this.formatNumber(row.入庫原価)}</td>
                <td class="number">${this.formatNumber(row.入庫売価)}</td>
                <td class="number">${this.formatNumber(row.廃棄金額)}</td>
                <td class="number">${this.formatNumber(row.値引金額)}</td>
                <td class="number">${this.formatNumber(row.在庫原価)}</td>
            </tr>
        `).join('');

        tableBody.innerHTML = rows;
    }

    formatNumber(value) {
        if (value === null || value === undefined || value === 0) {
            return '-';
        }
        return new Intl.NumberFormat('ja-JP').format(value);
    }

    updateTableInfo(result) {
        this.tableInfo.textContent = 
            `期間: ${result.start_date} ～ ${result.end_date} | 件数: ${result.total_records}件`;
    }

    exportToCSV() {
        if (this.currentData.length === 0) {
            alert('エクスポートするデータがありません。');
            return;
        }

        // CSVヘッダー（期間合計用）
        const headers = [
            '店舗コード', '店舗名', '部門コード', '部門名称', '課コード', '課名称',
            '売上金額(期間合計)', '仕入原価(期間合計)', '仕入売価(期間合計)', '返品原価(期間合計)', '返品売価(期間合計)',
            '出庫原価(期間合計)', '出庫売価(期間合計)', '入庫原価(期間合計)', '入庫売価(期間合計)',
            '廃棄金額(期間合計)', '値引金額(期間合計)', '在庫原価(期間合計)'
        ];

        // CSVデータ作成
        const csvContent = [
            headers.join(','),
            ...this.currentData.map(row => [
                row.店舗コード, `"${row.店舗名}"`, row.部門コード, `"${row.部門名称}"`,
                row.課コード, `"${row.課名称}"`, row.売上金額, row.仕入原価, row.仕入売価,
                row.返品原価, row.返品売価, row.出庫原価, row.出庫売価, row.入庫原価,
                row.入庫売価, row.廃棄金額, row.値引金額, row.在庫原価
            ].join(','))
        ].join('\n');

        // ファイルダウンロード
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        const startDate = this.startDateInput.value.replace(/-/g, '');
        const endDate = this.endDateInput.value.replace(/-/g, '');
        const filename = `部門別期間合計レポート_${startDate}_${endDate}.csv`;
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// ページ読み込み完了後に初期化
document.addEventListener('DOMContentLoaded', () => {
    new ReportManager();
});
