// 日次部門別レポート JavaScript

class ReportManager {
    constructor() {
        this.currentData = [];
        this.init();
    }

    init() {
        // DOM要素の取得
        this.startDateInput = document.getElementById('startDate');
        this.endDateInput = document.getElementById('endDate');
        this.searchBtn = document.getElementById('searchBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.loadingDiv = document.getElementById('loading');
        this.errorDiv = document.getElementById('error');
        this.tableContainer = document.getElementById('tableContainer');
        this.tableInfo = document.getElementById('tableInfo');

        // イベントリスナーの設定
        this.setupEventListeners();

        // 初期日付設定
        this.setDefaultDates();

        // 初期データ読み込み
        this.loadData();
    }

    setupEventListeners() {
        // 検索ボタン
        this.searchBtn.addEventListener('click', () => {
            this.loadData();
        });

        // エクスポートボタン
        this.exportBtn.addEventListener('click', () => {
            this.exportToCSV();
        });

        // Enterキーでの検索
        this.startDateInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.loadData();
        });

        this.endDateInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.loadData();
        });
    }

    setDefaultDates() {
        // デフォルトで過去30日間を設定
        const today = new Date();
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);

        this.endDateInput.value = this.formatDate(today);
        this.startDateInput.value = this.formatDate(thirtyDaysAgo);
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    showLoading() {
        this.loadingDiv.style.display = 'block';
        this.errorDiv.style.display = 'none';
        this.tableContainer.style.display = 'none';
    }

    hideLoading() {
        this.loadingDiv.style.display = 'none';
    }

    showError(message) {
        this.errorDiv.innerHTML = `<strong>エラー:</strong> ${message}`;
        this.errorDiv.style.display = 'block';
        this.tableContainer.style.display = 'none';
    }

    showTable() {
        this.errorDiv.style.display = 'none';
        this.tableContainer.style.display = 'block';
    }

    async loadData() {
        try {
            this.showLoading();

            const startDate = this.startDateInput.value;
            const endDate = this.endDateInput.value;

            // 日付バリデーション
            if (!startDate || !endDate) {
                throw new Error('開始日と終了日を入力してください。');
            }

            if (new Date(startDate) > new Date(endDate)) {
                throw new Error('開始日は終了日より前の日付を入力してください。');
            }

            // APIリクエスト
            const response = await fetch(`/api/report?start_date=${startDate}&end_date=${endDate}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'データの取得に失敗しました。');
            }

            this.currentData = result.data;
            this.renderTable(result.data);
            this.updateTableInfo(result);
            this.showTable();

        } catch (error) {
            console.error('データ読み込みエラー:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderTable(data) {
        const tableBody = document.getElementById('reportTableBody');
        
        if (data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="19" style="text-align: center; padding: 40px;">データがありません</td></tr>';
            return;
        }

        const rows = data.map(row => `
            <tr>
                <td>${row.営業日}</td>
                <td>${row.店舗コード}</td>
                <td style="text-align: left;">${row.店舗名}</td>
                <td>${row.部門コード}</td>
                <td style="text-align: left;">${row.部門名称}</td>
                <td>${row.課コード}</td>
                <td style="text-align: left;">${row.課名称}</td>
                <td class="number">${this.formatNumber(row.売上金額)}</td>
                <td class="number">${this.formatNumber(row.仕入原価)}</td>
                <td class="number">${this.formatNumber(row.仕入売価)}</td>
                <td class="number">${this.formatNumber(row.返品原価)}</td>
                <td class="number">${this.formatNumber(row.返品売価)}</td>
                <td class="number">${this.formatNumber(row.出庫原価)}</td>
                <td class="number">${this.formatNumber(row.出庫売価)}</td>
                <td class="number">${this.formatNumber(row.入庫原価)}</td>
                <td class="number">${this.formatNumber(row.入庫売価)}</td>
                <td class="number">${this.formatNumber(row.廃棄金額)}</td>
                <td class="number">${this.formatNumber(row.値引金額)}</td>
                <td class="number">${this.formatNumber(row.在庫原価)}</td>
            </tr>
        `).join('');

        tableBody.innerHTML = rows;
    }

    formatNumber(value) {
        if (value === null || value === undefined || value === 0) {
            return '-';
        }
        return new Intl.NumberFormat('ja-JP').format(value);
    }

    updateTableInfo(result) {
        this.tableInfo.textContent = 
            `期間: ${result.start_date} ～ ${result.end_date} | 件数: ${result.total_records}件`;
    }

    exportToCSV() {
        if (this.currentData.length === 0) {
            alert('エクスポートするデータがありません。');
            return;
        }

        // CSVヘッダー
        const headers = [
            '営業日', '店舗コード', '店舗名', '部門コード', '部門名称', '課コード', '課名称',
            '売上金額', '仕入原価', '仕入売価', '返品原価', '返品売価', '出庫原価', '出庫売価',
            '入庫原価', '入庫売価', '廃棄金額', '値引金額', '在庫原価'
        ];

        // CSVデータ作成
        const csvContent = [
            headers.join(','),
            ...this.currentData.map(row => [
                row.営業日, row.店舗コード, `"${row.店舗名}"`, row.部門コード, `"${row.部門名称}"`,
                row.課コード, `"${row.課名称}"`, row.売上金額, row.仕入原価, row.仕入売価,
                row.返品原価, row.返品売価, row.出庫原価, row.出庫売価, row.入庫原価,
                row.入庫売価, row.廃棄金額, row.値引金額, row.在庫原価
            ].join(','))
        ].join('\n');

        // ファイルダウンロード
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        const startDate = this.startDateInput.value.replace(/-/g, '');
        const endDate = this.endDateInput.value.replace(/-/g, '');
        const filename = `日次部門別レポート_${startDate}_${endDate}.csv`;
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// ページ読み込み完了後に初期化
document.addEventListener('DOMContentLoaded', () => {
    new ReportManager();
});
