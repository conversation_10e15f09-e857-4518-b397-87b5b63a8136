MERGE INTO DailyDeptPriceResults AS Target
USING (
    SELECT
        DR.StoreId,
        Sec.Id AS SectionId,
        DR.SalesDate,
        SUM(ISNULL(PP.PurchasePrice, 0)) AS PurchasePriceSum,
        SUM(ISNULL(RP.ReturnPrice, 0)) AS ReturnPriceSum,
        SUM(ISNULL(TSOut.TotalOutPrice, 0)) AS OutPriceSum,
        SUM(ISNULL(TSIn.TotalInPrice, 0)) AS InPriceSum
    FROM DailyDeptResults AS DR
    INNER JOIN Stores AS S ON DR.StoreId = S.Id
    INNER JOIN Sections AS Sec ON DR.SectionId = Sec.Id

    -- Purchase
    LEFT JOIN (
        SELECT
            PS.StoreId,
            Secs.Id AS SectionId,
            PS.SlipDate,
            SUM(CASE WHEN PS.UnitPrice = 0 THEN PS.UnitCost ELSE PS.UnitPrice END * PS.Quantity) AS PurchasePrice
        FROM PurchaseSlip AS PS
        INNER JOIN Items AS I ON PS.ItemId = I.Id
        INNER JOIN Sections AS Secs ON I.DeptId = Secs.Id
        WHERE PS.DelFlg = 0 AND PS.SlipType = '1'
        GROUP BY PS.StoreId, Secs.Id, PS.SlipDate
    ) AS PP ON DR.StoreId = PP.StoreId AND DR.SectionId = PP.SectionId AND DR.SalesDate = PP.SlipDate

    -- Return
    LEFT JOIN (
        SELECT
            PS.StoreId,
            Secs.Id AS SectionId,
            PS.SlipDate,
            SUM(CASE WHEN PS.UnitPrice = 0 THEN PS.UnitCost ELSE PS.UnitPrice END * PS.Quantity) AS ReturnPrice
        FROM PurchaseSlip AS PS
        INNER JOIN Items AS I ON PS.ItemId = I.Id
        INNER JOIN Sections AS Secs ON I.DeptId = Secs.Id
        WHERE PS.DelFlg = 0 AND PS.SlipType = '2'
        GROUP BY PS.StoreId, Secs.Id, PS.SlipDate
    ) AS RP ON DR.StoreId = RP.StoreId AND DR.SectionId = RP.SectionId AND DR.SalesDate = RP.SlipDate

    -- 出庫
    LEFT JOIN (
        SELECT
            TS.OutStoreId AS StoreId,
            TS.OutSectionId AS SectionId,
            TS.TransferDate,
            SUM(TS.Quantity * CASE WHEN I.UnitPrice = 0 THEN TS.UnitCost ELSE I.UnitPrice END) AS TotalOutPrice
        FROM TransferSlip AS TS
        INNER JOIN Items AS I ON TS.OutItemId = I.Id
        WHERE TS.DelFlg = 0
        GROUP BY TS.OutStoreId, TS.OutSectionId, TS.TransferDate
    ) AS TSOut ON DR.StoreId = TSOut.StoreId AND DR.SectionId = TSOut.SectionId AND DR.SalesDate = TSOut.TransferDate

    -- 入庫
    LEFT JOIN (
        SELECT
            TS.InStoreId AS StoreId,
            TS.InSectionId AS SectionId,
            TS.TransferDate,
            SUM(TS.Quantity * CASE WHEN I.UnitPrice = 0 THEN TS.UnitCost ELSE I.UnitPrice END) AS TotalInPrice
        FROM TransferSlip AS TS
        INNER JOIN Items AS I ON TS.OutItemId = I.Id
        WHERE TS.DelFlg = 0
        GROUP BY TS.InStoreId, TS.InSectionId, TS.TransferDate
    ) AS TSIn ON DR.StoreId = TSIn.StoreId AND DR.SectionId = TSIn.SectionId AND DR.SalesDate = TSIn.TransferDate

    WHERE DR.SalesDate BETWEEN '2025-02-01' AND '2025-02-05'

    GROUP BY
        DR.StoreId,
        Sec.Id,
        DR.SalesDate
) AS Source
ON (
    Target.StoreId = Source.StoreId AND
    Target.SectionId = Source.SectionId AND
    Target.SalesDate = Source.SalesDate
)
WHEN MATCHED THEN
    UPDATE SET
        Target.PurchasePriceSum = Source.PurchasePriceSum,
        Target.ReturnPriceSum = Source.ReturnPriceSum,
        Target.OutPriceSum = Source.OutPriceSum,
        Target.InPriceSum = Source.InPriceSum
WHEN NOT MATCHED THEN
    INSERT (
        StoreId,
        SectionId,
        SalesDate,
        PurchasePriceSum,
        ReturnPriceSum,
        OutPriceSum,
        InPriceSum
    )
    VALUES (
        Source.StoreId,
        Source.SectionId,
        Source.SalesDate,
        Source.PurchasePriceSum,
        Source.ReturnPriceSum,
        Source.OutPriceSum,
        Source.InPriceSum
    );
