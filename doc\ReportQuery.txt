SELECT 
    DailyDeptResults.SalesDate AS 営業日,
    Stores.StoreCode AS 店舗コード,
    Stores.StoreName AS 店舗名,
    Sections_1.SectionCode AS 部門コード,
    Sections_1.SectionName AS 部門名称,
    Sections.SectionCode AS 課コード,
    Sections.SectionName AS 課名称,
    DailyDeptResults.SalesAmount AS 売上金額,
    DailyDeptResults.PurchaseAmount AS 仕入原価,
    DailyDeptPriceResults.PurchasePriceSum AS 仕入売価,
    DailyDeptResults.ReturnAmount AS 返品原価,
    DailyDeptPriceResults.ReturnPriceSum AS 返品売価,
    DailyDeptResults.TransferOutAmount AS 出庫原価,
    DailyDeptPriceResults.OutPriceSum AS 出庫売価,
    DailyDeptResults.TransferInAmount AS 入庫原価,
    DailyDeptPriceResults.InPriceSum AS 入庫売価,
    DailyDeptResults.DisposalAmountPrice AS 廃棄金額,
    DailyDeptResults.DiscountAmount AS 値引金額,
    DailyDeptResults.InventoryAomunt AS 在庫原価
FROM 
    Stores
    INNER JOIN DailyDeptResults 
        ON Stores.Id = DailyDeptResults.StoreId
    INNER JOIN Sections 
        ON DailyDeptResults.SectionId = Sections.Id
    INNER JOIN Sections AS Sections_1 
        ON Sections.ParentCodeId = Sections_1.Id
    LEFT OUTER JOIN DailyDeptPriceResults 
        ON DailyDeptResults.StoreId = DailyDeptPriceResults.StoreId 
        AND DailyDeptResults.SectionId = DailyDeptPriceResults.SectionId 
        AND DailyDeptResults.SalesDate = DailyDeptPriceResults.SalesDate
WHERE 
    Stores.DelFlg = 0
    AND Sections_1.DelFlg = 0
    AND Sections.DelFlg = 0;
