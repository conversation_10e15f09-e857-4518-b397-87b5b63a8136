# 日次部門別価格更新バッチ

このバッチプログラムは、SQL ServerのYszInventoryデータベースの`DailyDeptPriceResults`テーブルを更新するためのPythonバッチです。

## 機能概要

- システム日付から指定された遡り日数分の期間のデータを処理
- 購入、返品、出庫、入庫の価格データを集計してMERGE処理
- 詳細なログ出力（開始・終了時刻、処理件数など）
- 設定ファイルによる柔軟な設定管理

## ファイル構成

```
YszSlipPriceUpdate/
├── main.py              # メインバッチプログラム
├── config.json          # 設定ファイル
├── requirements.txt     # 必要なPythonパッケージ
├── run_batch.bat        # バッチ実行用バッチファイル
├── README.md           # このファイル
├── doc/
│   └── InsertUpdateQuery.txt  # 元のSQLクエリ
└── logs/               # ログフォルダ（自動作成）
    └── batch_YYYYMMDD_HHMMSS.log
```

## セットアップ手順

### 1. 必要なソフトウェア

- Python 3.9以上
- Microsoft ODBC Driver 17 for SQL Server

### 2. ODBC Driverのインストール

Microsoft公式サイトから「Microsoft ODBC Driver 17 for SQL Server」をダウンロードしてインストールしてください。

### 3. Pythonパッケージのインストール

```bash
pip install -r requirements.txt
```

### 4. 設定ファイルの編集

`config.json`を環境に合わせて編集してください：

```json
{
    "database": {
        "server": "localhost",          # SQL Serverのサーバー名
        "database": "YszInventory",     # データベース名
        "username": "sa",               # ユーザー名
        "password": "Ysz001",           # パスワード
        "driver": "ODBC Driver 17 for SQL Server"
    },
    "batch_settings": {
        "lookback_days": 5,             # 遡り日数
        "timeout_seconds": 300          # タイムアウト秒数
    }
}
```

## 実行方法

### 手動実行

1. **Pythonで直接実行**
   ```bash
   python main.py
   ```

2. **バッチファイルで実行**
   ```bash
   run_batch.bat
   ```

### Windowsタスクスケジューラでの自動実行

1. Windowsタスクスケジューラを開く
2. 「基本タスクの作成」を選択
3. 以下の設定を行う：
   - **名前**: 日次部門別価格更新バッチ
   - **トリガー**: 毎日（実行したい時刻を設定）
   - **操作**: プログラムの開始
   - **プログラム/スクリプト**: `C:\YszSlipPriceUpdate\run_batch.bat`
   - **引数**: `scheduled`
   - **開始**: `C:\YszSlipPriceUpdate`

## ログ出力

実行ログは`logs`フォルダに以下の形式で出力されます：

- ファイル名: `batch_YYYYMMDD_HHMMSS.log`
- 内容:
  - 開始・終了時刻（ミリ秒まで）
  - 処理対象期間
  - データベース接続状況
  - 処理件数
  - エラー情報（発生時）

### ログ出力例

```
2025-01-20 09:00:00.123 [INFO] ============================================================
2025-01-20 09:00:00.124 [INFO] 日次部門別価格更新バッチを開始します
2025-01-20 09:00:00.125 [INFO] 開始時刻: 2025-01-20 09:00:00.123
2025-01-20 09:00:00.126 [INFO] 設定ファイルを読み込みました
2025-01-20 09:00:00.127 [INFO] 処理対象期間: 2025-01-16 ～ 2025-01-20
2025-01-20 09:00:00.128 [INFO] データベースに接続しました
2025-01-20 09:00:00.129 [INFO] MERGEクエリを実行開始
2025-01-20 09:00:01.456 [INFO] MERGEクエリを実行完了 - 処理件数: 150件
2025-01-20 09:00:01.457 [INFO] データベース接続を閉じました
2025-01-20 09:00:01.458 [INFO] 処理が正常に完了しました
2025-01-20 09:00:01.459 [INFO] 終了時刻: 2025-01-20 09:00:01.458
2025-01-20 09:00:01.460 [INFO] 実行時間: 1.337秒
2025-01-20 09:00:01.461 [INFO] 処理件数: 150件
2025-01-20 09:00:01.462 [INFO] ============================================================
```

## 処理内容

バッチは以下の処理を行います：

1. **日付範囲の計算**
   - システム日付を取得
   - config.jsonの`lookback_days`設定から遡り日数を取得
   - 処理対象期間を決定（遡り日数前の日付～当日）

2. **データ集計**
   - 購入データ（PurchaseSlip）
   - 返品データ（PurchaseSlip）
   - 出庫データ（TransferSlip）
   - 入庫データ（TransferSlip）

3. **MERGE処理**
   - 既存データがある場合は更新
   - 既存データがない場合は挿入

## トラブルシューティング

### よくあるエラー

1. **データベース接続エラー**
   - SQL Serverが起動しているか確認
   - config.jsonの接続情報が正しいか確認
   - ODBC Driverがインストールされているか確認

2. **権限エラー**
   - 指定したユーザーに必要なテーブルへのアクセス権限があるか確認

3. **タイムアウトエラー**
   - config.jsonの`timeout_seconds`を増やす
   - データ量が多い場合は処理時間が長くなる可能性があります

### ログの確認

エラーが発生した場合は、`logs`フォルダ内の最新のログファイルを確認してください。

## 注意事項

- バッチ実行前にデータベースのバックアップを取ることを推奨します
- 本番環境での初回実行時は、少ない日数で動作確認を行ってください
- 大量データ処理時は、実行時間とシステムリソースに注意してください
