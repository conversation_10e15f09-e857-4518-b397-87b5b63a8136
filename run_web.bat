@echo off
REM Daily Department Report Web Application Execution File
REM
REM Usage:
REM   1. Double-click this batch file to execute
REM   2. Access http://localhost:5000 in your browser
REM

cd /d "%~dp0"

echo ========================================
echo Starting Daily Department Report Web Application
echo Execution time: %date% %time%
echo ========================================

REM Check if required packages are installed
python -c "import flask, sqlalchemy, pyodbc" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    python -m pip install Flask==3.0.0 SQLAlchemy==2.0.23 pyodbc==5.0.1 python-dateutil==2.8.2 Werkzeug==3.0.1
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install packages. Please install manually:
        echo pip install Flask SQLAlchemy pyodbc python-dateutil Werkzeug
        pause
        exit /b 1
    )
)

REM Execute Python web application
echo Starting web server...
echo Access URL: http://localhost:5000
echo Press Ctrl+C to stop the server
echo ========================================
python app.py

REM Check execution result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Web application stopped normally
    echo End time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Web application stopped with error ^(Exit code: %ERRORLEVEL%^)
    echo End time: %date% %time%
    echo Please check the error messages above
    echo ========================================
)

pause
exit /b %ERRORLEVEL%
