<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部門別期間合計レポート</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <!-- ヘッダー -->
        <header class="header">
            <h1>部門別期間合計レポート</h1>
        </header>

        <!-- フィルター部分 -->
        <section class="filter-section">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="startDate">開始日</label>
                    <input type="date" id="startDate" name="startDate">
                </div>

                <div class="filter-group">
                    <label for="endDate">終了日</label>
                    <input type="date" id="endDate" name="endDate">
                </div>

                <div class="filter-group">
                    <label for="companySelect">法人</label>
                    <select id="companySelect" name="company">
                        <option value="未指定">未指定</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="storeSelect">店舗</label>
                    <select id="storeSelect" name="store">
                        <option value="未指定">未指定</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="departmentSelect">部門</label>
                    <select id="departmentSelect" name="department">
                        <option value="未指定">未指定</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="sectionSelect">課</label>
                    <select id="sectionSelect" name="section" disabled>
                        <option value="未指定">未指定</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button id="searchBtn" class="btn btn-primary">検索</button>
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button id="exportBtn" class="btn btn-secondary">CSV出力</button>
                </div>
            </div>
        </section>

        <!-- ローディング -->
        <div id="loading" class="loading" style="display: none;">
            データを読み込み中です...
        </div>

        <!-- エラーメッセージ -->
        <div id="error" class="error" style="display: none;"></div>

        <!-- テーブル -->
        <div id="tableContainer" class="table-container" style="display: none;">
            <div class="table-header">
                <h2>レポート結果</h2>
                <div id="tableInfo" class="table-info"></div>
            </div>
            
            <div class="table-wrapper">
                <table>
                    <thead>
                        <tr>
                            <th>店舗<br>コード</th>
                            <th>店舗名</th>
                            <th>部門<br>コード</th>
                            <th>部門名称</th>
                            <th>課<br>コード</th>
                            <th>課名称</th>
                            <th>売上金額<br>(期間合計)</th>
                            <th>仕入原価<br>(期間合計)</th>
                            <th>仕入売価<br>(期間合計)</th>
                            <th>返品原価<br>(期間合計)</th>
                            <th>返品売価<br>(期間合計)</th>
                            <th>出庫原価<br>(期間合計)</th>
                            <th>出庫売価<br>(期間合計)</th>
                            <th>入庫原価<br>(期間合計)</th>
                            <th>入庫売価<br>(期間合計)</th>
                            <th>廃棄金額<br>(期間合計)</th>
                            <th>値引金額<br>(期間合計)</th>
                            <th>在庫原価<br>(期間合計)</th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody">
                        <!-- データはJavaScriptで動的に挿入 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/report.js') }}"></script>
</body>
</html>
