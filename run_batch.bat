@echo off
REM Daily Department Price Update Batch Execution File
REM
REM Usage:
REM   1. Double-click this batch file to execute
REM   2. Execute from Windows Task Scheduler
REM

cd /d "%~dp0"

echo ========================================
echo Starting Daily Department Price Update Batch
echo Execution time: %date% %time%
echo ========================================

REM Activate Python virtual environment if exists
REM call venv\Scripts\activate.bat

REM Execute Python batch
python main.py

REM Check execution result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Batch completed successfully
    echo End time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Batch execution failed ^(Exit code: %ERRORLEVEL%^)
    echo End time: %date% %time%
    echo Please check the log file
    echo ========================================
)

REM Auto-close after 3 seconds for manual execution
if "%1" NEQ "scheduled" (
    timeout /t 3 /nobreak >nul
)

exit /b %ERRORLEVEL%
