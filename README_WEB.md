# 日次部門別レポート Web アプリケーション

このWebアプリケーションは、日次部門別の売上・仕入・在庫データをブラウザで閲覧できるレポート画面です。

## 機能概要

- **レポート表示**: 日次部門別の詳細データをテーブル形式で表示
- **日付フィルタ**: 開始日・終了日を指定してデータを絞り込み
- **CSV出力**: 表示中のデータをCSVファイルとしてダウンロード
- **レスポンシブデザイン**: PC・タブレット・スマートフォンに対応

## 技術構成

### サーバーサイド
- **Python 3.12**
- **Flask 3.0.0**: Webアプリケーションフレームワーク
- **SQL Alchemy 2.0.23**: データベースORM
- **pyodbc 5.0.1**: SQL Server接続

### フロントエンド
- **HTML5**: セマンティックなマークアップ
- **CSS3**: レスポンシブデザイン、グラデーション、アニメーション
- **JavaScript (ES6+)**: 非同期データ取得、CSV出力、UI制御

## ファイル構成

```
YszSlipPriceUpdate/
├── app.py                    # Flask Webアプリケーション
├── models.py                 # SQL Alchemyモデル定義
├── config_web.json          # Web用設定ファイル
├── requirements_web.txt     # Web用Pythonパッケージ
├── run_web.bat              # Web起動用バッチファイル
├── README_WEB.md            # このファイル
├── templates/
│   └── report.html          # レポート画面HTMLテンプレート
└── static/
    ├── css/
    │   └── style.css        # スタイルシート
    └── js/
        └── report.js        # レポート用JavaScript
```

## セットアップ手順

### 1. 必要なソフトウェア
- Python 3.9以上
- Microsoft ODBC Driver 17 for SQL Server

### 2. Pythonパッケージのインストール

```bash
pip install -r requirements_web.txt
```

または個別にインストール：

```bash
pip install Flask==3.0.0 SQLAlchemy==2.0.23 pyodbc==5.0.1 python-dateutil==2.8.2 Werkzeug==3.0.1
```

### 3. 設定ファイルの編集

`config_web.json`を環境に合わせて編集してください：

```json
{
    "database": {
        "server": "************",
        "database": "YszInventory",
        "username": "sa",
        "password": "H360c0001",
        "driver": "ODBC Driver 17 for SQL Server"
    },
    "web_settings": {
        "host": "0.0.0.0",
        "port": 5000,
        "debug": true,
        "secret_key": "your-secret-key-here"
    }
}
```

## 実行方法

### 1. バッチファイルで実行（推奨）

```bash
run_web.bat
```

### 2. Pythonで直接実行

```bash
python app.py
```

### 3. ブラウザでアクセス

アプリケーション起動後、以下のURLにアクセス：

```
http://localhost:5000
```

## 使用方法

### 1. レポート表示
1. ブラウザでアプリケーションにアクセス
2. 開始日・終了日を入力（デフォルトは過去30日間）
3. 「検索」ボタンをクリック
4. テーブルにデータが表示されます

### 2. CSV出力
1. データを表示した状態で「CSV出力」ボタンをクリック
2. ファイル名：`日次部門別レポート_開始日_終了日.csv`
3. 自動的にダウンロードが開始されます

## 表示データ項目

| 項目名 | 説明 |
|--------|------|
| 営業日 | 売上日付 |
| 店舗コード | 店舗の識別コード |
| 店舗名 | 店舗名称 |
| 部門コード | 部門の識別コード |
| 部門名称 | 部門名称 |
| 課コード | 課の識別コード |
| 課名称 | 課名称 |
| 売上金額 | 売上金額 |
| 仕入原価 | 仕入原価 |
| 仕入売価 | 仕入売価 |
| 返品原価 | 返品原価 |
| 返品売価 | 返品売価 |
| 出庫原価 | 出庫原価 |
| 出庫売価 | 出庫売価 |
| 入庫原価 | 入庫原価 |
| 入庫売価 | 入庫売価 |
| 廃棄金額 | 廃棄金額 |
| 値引金額 | 値引金額 |
| 在庫原価 | 在庫原価 |

## API仕様

### GET /api/report

レポートデータを取得するAPIエンドポイント

**パラメータ:**
- `start_date` (optional): 開始日 (YYYY-MM-DD形式)
- `end_date` (optional): 終了日 (YYYY-MM-DD形式)

**レスポンス:**
```json
{
    "success": true,
    "data": [
        {
            "営業日": "2025-01-20",
            "店舗コード": "001",
            "店舗名": "本店",
            ...
        }
    ],
    "start_date": "2025-01-01",
    "end_date": "2025-01-20",
    "total_records": 100
}
```

## トラブルシューティング

### よくあるエラー

1. **データベース接続エラー**
   - SQL Serverが起動しているか確認
   - config_web.jsonの接続情報が正しいか確認
   - ODBC Driverがインストールされているか確認

2. **パッケージインポートエラー**
   - 必要なPythonパッケージがインストールされているか確認
   - `pip install -r requirements_web.txt`を実行

3. **ポート使用中エラー**
   - ポート5000が他のアプリケーションで使用されていないか確認
   - config_web.jsonでポート番号を変更

4. **データが表示されない**
   - 指定した日付範囲にデータが存在するか確認
   - データベースのテーブル構造が正しいか確認

## セキュリティ注意事項

- 本番環境では`debug: false`に設定してください
- `secret_key`を強力なランダム文字列に変更してください
- データベース接続情報を適切に保護してください
- 必要に応じてHTTPS化を検討してください

## カスタマイズ

### スタイルの変更
`static/css/style.css`を編集してデザインをカスタマイズできます。

### 機能の追加
`static/js/report.js`を編集してフロントエンド機能を追加できます。

### データベースクエリの変更
`app.py`の`get_report_data()`関数内のSQLクエリを変更できます。
