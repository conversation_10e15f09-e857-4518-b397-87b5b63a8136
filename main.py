#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Daily Department Price Update Batch
日次部門別価格更新バッチ

このバッチは、DailyDeptPriceResultsテーブルを更新します。
システム日付から遡り日数分の期間のデータを処理します。
"""

import os
import sys
import json
import logging
import pyodbc
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple


class DailyPriceUpdateBatch:
    """日次価格更新バッチクラス"""
    
    def __init__(self):
        """初期化"""
        self.config = None
        self.logger = None
        self.connection = None
        self.start_time = None
        self.end_time = None
        
    def setup_logging(self) -> None:
        """ログ設定"""
        # logsディレクトリの作成
        logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        
        # ログファイル名（実行日時）
        log_filename = datetime.now().strftime('batch_%Y%m%d_%H%M%S.log')
        log_filepath = os.path.join(logs_dir, log_filename)
        
        # ログ設定
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s.%(msecs)03d [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.FileHandler(log_filepath, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_config(self) -> Dict[str, Any]:
        """設定ファイル読み込み"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self.logger.info("設定ファイルを読み込みました")
            return self.config
        except FileNotFoundError:
            self.logger.error("config.jsonが見つかりません")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"config.jsonの形式が正しくありません: {e}")
            raise
            
    def get_date_range(self) -> Tuple[str, str]:
        """日付範囲を取得"""
        today = datetime.now().date()
        lookback_days = self.config['batch_settings']['lookback_days']
        
        start_date = today - timedelta(days=lookback_days - 1)
        end_date = today
        
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        self.logger.info(f"処理対象期間: {start_date_str} ～ {end_date_str}")
        return start_date_str, end_date_str
        
    def connect_database(self) -> None:
        """データベース接続"""
        try:
            db_config = self.config['database']
            connection_string = (
                f"DRIVER={{{db_config['driver']}}};"
                f"SERVER={db_config['server']};"
                f"DATABASE={db_config['database']};"
                f"UID={db_config['username']};"
                f"PWD={db_config['password']};"
                f"TrustServerCertificate=yes;"
            )
            
            self.connection = pyodbc.connect(
                connection_string,
                timeout=self.config['batch_settings']['timeout_seconds']
            )
            self.logger.info("データベースに接続しました")
            
        except pyodbc.Error as e:
            self.logger.error(f"データベース接続エラー: {e}")
            raise
            
    def get_merge_query(self, start_date: str, end_date: str) -> str:
        """MERGEクエリを取得"""
        return f"""
MERGE INTO DailyDeptPriceResults AS Target
USING (
    SELECT
        DR.StoreId,
        Sec.Id AS SectionId,
        DR.SalesDate,
        SUM(ISNULL(PP.PurchasePrice, 0)) AS PurchasePriceSum,
        SUM(ISNULL(RP.ReturnPrice, 0)) AS ReturnPriceSum,
        SUM(ISNULL(TSOut.TotalOutPrice, 0)) AS OutPriceSum,
        SUM(ISNULL(TSIn.TotalInPrice, 0)) AS InPriceSum
    FROM DailyDeptResults AS DR
    INNER JOIN Stores AS S ON DR.StoreId = S.Id
    INNER JOIN Sections AS Sec ON DR.SectionId = Sec.Id

    -- Purchase
    LEFT JOIN (
        SELECT
            PS.StoreId,
            Secs.Id AS SectionId,
            PS.SlipDate,
            SUM(CASE WHEN PS.UnitPrice = 0 THEN PS.UnitCost ELSE PS.UnitPrice END * PS.Quantity) AS PurchasePrice
        FROM PurchaseSlip AS PS
        INNER JOIN Items AS I ON PS.ItemId = I.Id
        INNER JOIN Sections AS Secs ON I.DeptId = Secs.Id
        WHERE PS.DelFlg = 0 AND PS.SlipType = '1'
        GROUP BY PS.StoreId, Secs.Id, PS.SlipDate
    ) AS PP ON DR.StoreId = PP.StoreId AND DR.SectionId = PP.SectionId AND DR.SalesDate = PP.SlipDate

    -- Return
    LEFT JOIN (
        SELECT
            PS.StoreId,
            Secs.Id AS SectionId,
            PS.SlipDate,
            SUM(CASE WHEN PS.UnitPrice = 0 THEN PS.UnitCost ELSE PS.UnitPrice END * PS.Quantity) AS ReturnPrice
        FROM PurchaseSlip AS PS
        INNER JOIN Items AS I ON PS.ItemId = I.Id
        INNER JOIN Sections AS Secs ON I.DeptId = Secs.Id
        WHERE PS.DelFlg = 0 AND PS.SlipType = '2'
        GROUP BY PS.StoreId, Secs.Id, PS.SlipDate
    ) AS RP ON DR.StoreId = RP.StoreId AND DR.SectionId = RP.SectionId AND DR.SalesDate = RP.SlipDate

    -- 出庫
    LEFT JOIN (
        SELECT
            TS.OutStoreId AS StoreId,
            TS.OutSectionId AS SectionId,
            TS.TransferDate,
            SUM(TS.Quantity * CASE WHEN I.UnitPrice = 0 THEN TS.UnitCost ELSE I.UnitPrice END) AS TotalOutPrice
        FROM TransferSlip AS TS
        INNER JOIN Items AS I ON TS.OutItemId = I.Id
        WHERE TS.DelFlg = 0
        GROUP BY TS.OutStoreId, TS.OutSectionId, TS.TransferDate
    ) AS TSOut ON DR.StoreId = TSOut.StoreId AND DR.SectionId = TSOut.SectionId AND DR.SalesDate = TSOut.TransferDate

    -- 入庫
    LEFT JOIN (
        SELECT
            TS.InStoreId AS StoreId,
            TS.InSectionId AS SectionId,
            TS.TransferDate,
            SUM(TS.Quantity * CASE WHEN I.UnitPrice = 0 THEN TS.UnitCost ELSE I.UnitPrice END) AS TotalInPrice
        FROM TransferSlip AS TS
        INNER JOIN Items AS I ON TS.OutItemId = I.Id
        WHERE TS.DelFlg = 0
        GROUP BY TS.InStoreId, TS.InSectionId, TS.TransferDate
    ) AS TSIn ON DR.StoreId = TSIn.StoreId AND DR.SectionId = TSIn.SectionId AND DR.SalesDate = TSIn.TransferDate

    WHERE DR.SalesDate BETWEEN '{start_date}' AND '{end_date}'

    GROUP BY
        DR.StoreId,
        Sec.Id,
        DR.SalesDate
) AS Source
ON (
    Target.StoreId = Source.StoreId AND
    Target.SectionId = Source.SectionId AND
    Target.SalesDate = Source.SalesDate
)
WHEN MATCHED THEN
    UPDATE SET
        Target.PurchasePriceSum = Source.PurchasePriceSum,
        Target.ReturnPriceSum = Source.ReturnPriceSum,
        Target.OutPriceSum = Source.OutPriceSum,
        Target.InPriceSum = Source.InPriceSum,
        Target.UpDateTime = GETDATE()
WHEN NOT MATCHED THEN
    INSERT (
        StoreId,
        SectionId,
        SalesDate,
        PurchasePriceSum,
        ReturnPriceSum,
        OutPriceSum,
        InPriceSum,
        CrDateTime,
        UpDateTime
    )
    VALUES (
        Source.StoreId,
        Source.SectionId,
        Source.SalesDate,
        Source.PurchasePriceSum,
        Source.ReturnPriceSum,
        Source.OutPriceSum,
        Source.InPriceSum,
        GETDATE(),
        GETDATE()
    );
"""
        
    def execute_merge(self, start_date: str, end_date: str) -> int:
        """MERGEクエリ実行"""
        try:
            cursor = self.connection.cursor()
            query = self.get_merge_query(start_date, end_date)
            
            self.logger.info("MERGEクエリを実行開始")
            cursor.execute(query)
            
            # 影響を受けた行数を取得
            affected_rows = cursor.rowcount
            
            # コミット
            self.connection.commit()
            cursor.close()
            
            self.logger.info(f"MERGEクエリを実行完了 - 処理件数: {affected_rows}件")
            return affected_rows
            
        except pyodbc.Error as e:
            self.logger.error(f"クエリ実行エラー: {e}")
            if self.connection:
                self.connection.rollback()
            raise
            
    def cleanup(self) -> None:
        """リソースクリーンアップ"""
        if self.connection:
            self.connection.close()
            self.logger.info("データベース接続を閉じました")
            
    def run(self) -> None:
        """メイン処理"""
        try:
            # 開始時刻記録
            self.start_time = datetime.now()
            
            # ログ設定
            self.setup_logging()
            self.logger.info("=" * 60)
            self.logger.info("日次部門別価格更新バッチを開始します")
            self.logger.info(f"開始時刻: {self.start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            
            # 設定読み込み
            self.load_config()
            
            # 日付範囲取得
            start_date, end_date = self.get_date_range()
            
            # データベース接続
            self.connect_database()
            
            # MERGEクエリ実行
            affected_rows = self.execute_merge(start_date, end_date)
            
            # 終了時刻記録
            self.end_time = datetime.now()
            execution_time = self.end_time - self.start_time
            
            self.logger.info(f"処理が正常に完了しました")
            self.logger.info(f"終了時刻: {self.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            self.logger.info(f"実行時間: {execution_time.total_seconds():.3f}秒")
            self.logger.info(f"処理件数: {affected_rows}件")
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.end_time = datetime.now()
            if self.logger:
                self.logger.error(f"バッチ処理でエラーが発生しました: {e}")
                if self.start_time:
                    execution_time = self.end_time - self.start_time
                    self.logger.error(f"エラー発生時刻: {self.end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                    self.logger.error(f"実行時間: {execution_time.total_seconds():.3f}秒")
                self.logger.error("=" * 60)
            raise
        finally:
            self.cleanup()


def main():
    """メイン関数"""
    batch = DailyPriceUpdateBatch()
    try:
        batch.run()
        return 0
    except Exception as e:
        print(f"バッチ実行エラー: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
