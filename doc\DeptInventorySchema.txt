USE [YszInventory]
GO

/****** Object:  Table [dbo].[DeptInventory]    Script Date: 2025/06/02 17:35:04 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[DeptInventory](
	[StoreId] [bigint] NOT NULL,
	[SectionId] [bigint] NOT NULL,
	[InventoryDate] [date] NOT NULL,
	[InventoryCount] [bigint] NOT NULL,
	[InventoryAmount] [bigint] NOT NULL,
	[CrDateTime] [datetime] NOT NULL,
	[UpDateTime] [datetime] NOT NULL,
	[ManualInputFlg] [bit] NOT NULL,
	[CalcInventoryCount] [bigint] NOT NULL,
	[CalcInventoryAmount] [bigint] NOT NULL,
	[UpdateIpAddress] [nvarchar](15) NULL,
	[InventoryItemPrice] [bigint] NOT NULL,
	[CalcInventoryItemPrice] [bigint] NOT NULL,
	[InventoryDeptPrice] [bigint] NOT NULL,
	[InventoryDeptCost] [bigint] NOT NULL,
 CONSTRAINT [PK_DeptInventory] PRIMARY KEY CLUSTERED 
(
	[StoreId] ASC,
	[SectionId] ASC,
	[InventoryDate] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  CONSTRAINT [DF_DeptInventory_InventoryCount]  DEFAULT ((0)) FOR [InventoryCount]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  CONSTRAINT [DF_DeptInventory_InventoryAomunt]  DEFAULT ((0)) FOR [InventoryAmount]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  CONSTRAINT [DF_DeptInventory_CrDateTime]  DEFAULT (getdate()) FOR [CrDateTime]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  CONSTRAINT [DF_DeptInventory_UpDateTime]  DEFAULT (getdate()) FOR [UpDateTime]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  CONSTRAINT [DF__DeptInven__Manua__6A06A917]  DEFAULT ((0)) FOR [ManualInputFlg]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  CONSTRAINT [DF__DeptInven__CalcI__6AFACD50]  DEFAULT ((0)) FOR [CalcInventoryCount]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  CONSTRAINT [DF__DeptInven__CalcI__6BEEF189]  DEFAULT ((0)) FOR [CalcInventoryAmount]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  DEFAULT ((0)) FOR [InventoryItemPrice]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  DEFAULT ((0)) FOR [CalcInventoryItemPrice]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  DEFAULT ((0)) FOR [InventoryDeptPrice]
GO

ALTER TABLE [dbo].[DeptInventory] ADD  DEFAULT ((0)) FOR [InventoryDeptCost]
GO

ALTER TABLE [dbo].[DeptInventory]  WITH CHECK ADD  CONSTRAINT [FK_DeptInventory_Sections] FOREIGN KEY([SectionId])
REFERENCES [dbo].[Sections] ([Id])
GO

ALTER TABLE [dbo].[DeptInventory] CHECK CONSTRAINT [FK_DeptInventory_Sections]
GO

ALTER TABLE [dbo].[DeptInventory]  WITH CHECK ADD  CONSTRAINT [FK_DeptInventory_Stores] FOREIGN KEY([StoreId])
REFERENCES [dbo].[Stores] ([Id])
GO

ALTER TABLE [dbo].[DeptInventory] CHECK CONSTRAINT [FK_DeptInventory_Stores]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'店舗ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'DeptInventory', @level2type=N'COLUMN',@level2name=N'StoreId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部門ID（CodeLevel=1）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'DeptInventory', @level2type=N'COLUMN',@level2name=N'SectionId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'棚卸日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'DeptInventory', @level2type=N'COLUMN',@level2name=N'InventoryDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'棚卸数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'DeptInventory', @level2type=N'COLUMN',@level2name=N'InventoryCount'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'棚卸金額(原価)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'DeptInventory', @level2type=N'COLUMN',@level2name=N'InventoryAmount'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作成日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'DeptInventory', @level2type=N'COLUMN',@level2name=N'CrDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'DeptInventory', @level2type=N'COLUMN',@level2name=N'UpDateTime'
GO


