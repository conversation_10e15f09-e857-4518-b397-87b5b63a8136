#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Alchemy Models for Report System
レポートシステム用SQLAlchemyモデル
"""

from sqlalchemy import Column, Integer, String, DateTime, Numeric, Date, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()


class Store(Base):
    """店舗テーブル"""
    __tablename__ = 'Stores'
    
    Id = Column(Integer, primary_key=True)
    StoreCode = Column(String(50))
    StoreName = Column(String(100))
    DelFlg = Column(Integer, default=0)
    CrDateTime = Column(DateTime, default=datetime.now)
    UpDateTime = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class Section(Base):
    """部門・課テーブル"""
    __tablename__ = 'Sections'
    
    Id = Column(Integer, primary_key=True)
    SectionCode = Column(String(50))
    SectionName = Column(String(100))
    ParentCodeId = Column(Integer, ForeignKey('Sections.Id'))
    DelFlg = Column(Integer, default=0)
    CrDateTime = Column(DateTime, default=datetime.now)
    UpDateTime = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 自己参照リレーション
    parent = relationship("Section", remote_side=[Id])


class DailyDeptResult(Base):
    """日次部門別実績テーブル"""
    __tablename__ = 'DailyDeptResults'
    
    Id = Column(Integer, primary_key=True)
    StoreId = Column(Integer, ForeignKey('Stores.Id'))
    SectionId = Column(Integer, ForeignKey('Sections.Id'))
    SalesDate = Column(Date)
    SalesAmount = Column(Numeric(18, 2))
    PurchaseAmount = Column(Numeric(18, 2))
    ReturnAmount = Column(Numeric(18, 2))
    TransferOutAmount = Column(Numeric(18, 2))
    TransferInAmount = Column(Numeric(18, 2))
    DisposalAmountPrice = Column(Numeric(18, 2))
    DiscountAmount = Column(Numeric(18, 2))
    InventoryAomunt = Column(Numeric(18, 2))  # 元のテーブルのスペルミスを維持
    CrDateTime = Column(DateTime, default=datetime.now)
    UpDateTime = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # リレーション
    store = relationship("Store")
    section = relationship("Section")


class DailyDeptPriceResult(Base):
    """日次部門別価格実績テーブル"""
    __tablename__ = 'DailyDeptPriceResults'
    
    Id = Column(Integer, primary_key=True)
    StoreId = Column(Integer, ForeignKey('Stores.Id'))
    SectionId = Column(Integer, ForeignKey('Sections.Id'))
    SalesDate = Column(Date)
    PurchasePriceSum = Column(Numeric(18, 2))
    ReturnPriceSum = Column(Numeric(18, 2))
    OutPriceSum = Column(Numeric(18, 2))
    InPriceSum = Column(Numeric(18, 2))
    CrDateTime = Column(DateTime, default=datetime.now)
    UpDateTime = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # リレーション
    store = relationship("Store")
    section = relationship("Section")
